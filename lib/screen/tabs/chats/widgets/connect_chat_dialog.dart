import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_state.dart';

/// Dialog for connecting to a new chat by entering inbox ID
class ConnectChatDialog extends StatefulWidget {
  const ConnectChatDialog({super.key});

  @override
  State<ConnectChatDialog> createState() => _ConnectChatDialogState();
}

class _ConnectChatDialogState extends State<ConnectChatDialog> {
  final TextEditingController _inboxIdController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Auto-focus the text field when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _inboxIdController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<XmtpCubit, XmtpState>(
      listener: (context, state) {
        if (state is XmtpConversationCreated) {
          // Success - close dialog
          Navigator.of(context).pop(true);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Chat connected successfully!'),
              backgroundColor: Color(0xFF10B981),
            ),
          );
        } else if (state is XmtpError) {
          // Error - show error message
          setState(() {
            _isLoading = false;
            _errorMessage = state.message;
          });
        } else if (state is XmtpCreatingConversation) {
          // Loading state
          setState(() {
            _isLoading = true;
            _errorMessage = null;
          });
        }
      },
      child: AlertDialog(
        title: const Text(
          'Connect New Chat',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Enter the inbox ID of the person you want to chat with:',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF6B7280),
                ),
              ),
              const SizedBox(height: 16),

              // Inbox ID Input
              TextField(
                controller: _inboxIdController,
                focusNode: _focusNode,
                enabled: !_isLoading,
                decoration: InputDecoration(
                  labelText: 'Inbox ID',
                  hintText: 'Enter inbox ID...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.person_outline),
                  errorText: _errorMessage,
                ),
                onChanged: (value) {
                  if (_errorMessage != null) {
                    setState(() {
                      _errorMessage = null;
                    });
                  }
                },
                onSubmitted: (value) {
                  if (value.isNotEmpty && !_isLoading) {
                    _connectChat();
                  }
                },
              ),

              if (_isLoading) ...[
                const SizedBox(height: 16),
                const Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    SizedBox(width: 12),
                    Text(
                      'Connecting...',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: _isLoading || _inboxIdController.text.trim().isEmpty
                ? null
                : _connectChat,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3B82F6),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('Connect'),
          ),
        ],
      ),
    );
  }

  void _connectChat() {
    final inboxId = _inboxIdController.text.trim();

    if (inboxId.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter an inbox ID';
      });
      return;
    }

    // Enhanced validation for inbox ID format
    if (inboxId.length < 10) {
      setState(() {
        _errorMessage = 'Inbox ID is too short';
      });
      return;
    }

    // Check if trying to connect to self
    final xmtpCubit = context.read<XmtpCubit>();
    final currentInboxId = xmtpCubit.inboxId;
    if (currentInboxId != null && inboxId == currentInboxId) {
      setState(() {
        _errorMessage = 'Cannot connect to your own inbox ID';
      });
      return;
    }

    // Clear any previous error
    setState(() {
      _errorMessage = null;
    });

    // Create DM conversation
    xmtpCubit.createDm(inboxId);
  }
}

/// Helper function to show the connect chat dialog
Future<bool?> showConnectChatDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => const ConnectChatDialog(),
  );
}
