import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_mesh/cubit/theme/theme_cubit.dart';
import 'package:toii_mesh/flavors/flavors.dart';
import 'package:toii_mesh/generated/l10n.dart';
import 'package:toii_mesh/locator/locator.dart';
import 'package:toii_mesh/router/app_router.dart';

void main() async {
  F.appFlavor = Flavor.dev;
  WidgetsFlutterBinding.ensureInitialized();

  await setupLocator();

  // Register the adapter for the Transaction class
  runApp(const App());
}

class App extends StatelessWidget {
  const App({super.key});
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: GetIt.instance<ThemeCubit>()),
        //  BlocProvider.value(value: GetIt.instance<AuthCubit>()),
      ],
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (BuildContext ctx, state) {
          return MaterialApp.router(
            debugShowCheckedModeBanner: false,
            themeMode: ThemeMode.light,
            title: "Gao Social",
            // theme: AppTheme.lightTheme,
            supportedLocales: const <Locale>[
              Locale.fromSubtags(languageCode: 'en'),
            ],
            //  locale: Locale(languageCode),
            //darkTheme: AppTheme.darkTheme,
            routerConfig: router,
            builder: EasyLoading.init(),
            localizationsDelegates: const [
              S.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
          );
        },
      ),
    );
  }
}
