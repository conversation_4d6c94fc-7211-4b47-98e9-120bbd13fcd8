import 'dart:io';

import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get_it/get_it.dart';
import 'package:passkeys/authenticator.dart';
import 'package:toii_mesh/core/constant/enum_constant.dart';
import 'package:toii_mesh/core/constant/key_shared.dart';
import 'package:toii_mesh/core/repository/auth_repository.dart' show AuthRepository;
import 'package:toii_mesh/core/service/pass_key/auth_passkey_service.dart';
import 'package:toii_mesh/core/service/pass_key/local_relying_party_server.dart';
import 'package:toii_mesh/cubit/auth/create_account/create_account_cubit.dart';
import 'package:toii_mesh/model/auth/login/login_model.dart';
import 'package:toii_mesh/model/auth/login_gmail/login_gmail_request_model.dart';
import 'package:toii_mesh/model/auth/login_wallet/login_wallet.dart';
import 'package:toii_mesh/model/auth/wallet_passkey/wallet_passkey_register_request_model.dart';
import 'package:toii_mesh/model/auth/wallet_passkey/webauthn_response_model.dart';
import 'package:toii_mesh/utils/device_id/device_id_utils.dart';
import 'package:toii_mesh/utils/shared_prefs/shared_prefs.dart';
import 'package:toii_mesh/utils/utils/validator.dart';
 

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit() : super(const LoginState());
  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();
  final AuthPassKeyService _authPassKeyService = AuthPassKeyService(
    rps: LocalRelyingPartyServer(),
    authenticator: PasskeyAuthenticator(debugMode: kDebugMode),
  );

  void login({required String userName, required String password}) async {
    try {
      EasyLoading.show();
      emit(state.copyWith(status: LoginStatus.loading));
      final request = LoginRequestModel(
        email: userName.isValidEmail() ? userName.trim().toLowerCase() : null,
        phoneNumber: userName.isPhoneNoValid ? userName.trim() : null,
        password: password,
      );
      final result = await _authRepository.login(request);
      SharedPref.setBool(KeyShared.isLogin, true);
      SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
      emit(state.copyWith(status: LoginStatus.success));
    } on DioException catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: LoginStatus.failure,
          message: "Something went wrong",
        ),
      );
    } finally {
      EasyLoading.dismiss();
    }
  }

  void loginWithGmail({
    required String accessToken,
    required String idToken,
  }) async {
    try {
      emit(state.copyWith(status: LoginStatus.loading));
      final request = LoginGmailRequestModel(
        accessToken: accessToken,
        idToken: idToken,
      );
      final result = await _authRepository.tokenWithGmail(request);

      if (result.data.isNewUser == false) {
        SharedPref.setBool(KeyShared.isLogin, true);
        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(state.copyWith(status: LoginStatus.success));
      } else {
        if (result.data.tmpToken?.isNotEmpty == true) {
          SharedPref.setString(KeyShared.tmpTokenKey, result.data.tmpToken!);
        }

        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(
          state.copyWith(
            status: LoginStatus.newUser,
            registerType: RegisterType.social,
          ),
        );
      }
    } on DioException catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: LoginStatus.failure,
          message: "Something went wrong",
        ),
      );
    }
  }

  void loginWithApple({required String idToken}) async {
    try {
      emit(state.copyWith(status: LoginStatus.loading));
      final request = LoginAppleRequestModel(idToken: idToken);
      final result = await _authRepository.tokenWithApple(request);

      if (result.data.isNewUser == false) {
        SharedPref.setBool(KeyShared.isLogin, true);
        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(state.copyWith(status: LoginStatus.success));
      } else {
        if (result.data.tmpToken?.isNotEmpty == true) {
          SharedPref.setString(KeyShared.tmpTokenKey, result.data.tmpToken!);
        }

        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(
          state.copyWith(
            status: LoginStatus.newUser,
            registerType: RegisterType.social,
          ),
        );
      }
    } on DioException catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: LoginStatus.failure,
          message: "Something went wrong",
        ),
      );
    }
  }

  void loginWithWallet({
    required String address,
    required String signature,
  }) async {
    try {
      emit(state.copyWith(status: LoginStatus.loading));
      final request = WalletLoginRequestModel(
        address: address,
        signature: signature,
        walletProvider: "metamask",
      );
      final result = await _authRepository.loginWithWallet(request);
      if (result.data.tmpToken?.isNotEmpty == true) {
        SharedPref.setString(KeyShared.tmpTokenKey, result.data.tmpToken!);
      }
      if (result.data.isNewUser == false) {
        SharedPref.setBool(KeyShared.isLogin, true);
        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(state.copyWith(status: LoginStatus.success));
      } else {
        SharedPref.setString(KeyShared.walletCurrentKey, address);

        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(
          state.copyWith(
            status: LoginStatus.newUser,
            registerType: RegisterType.wallet,
          ),
        );
      }
    } on DioException catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: LoginStatus.failure,
          message: "Something went wrong",
        ),
      );
    }
  }

  Future<String?> getNonceMessageForWalletSignature(String address) async {
    try {
      final result = await _authRepository.getNonceMessageForWalletSignature(
        address,
      );
      return result.data.message;
    } catch (e) {
      rethrow;
    }
  }

  void loginWithPasskey({
    required String userName,
    required String userId,
  }) async {
    try {
      EasyLoading.show();
      emit(state.copyWith(status: LoginStatus.loading));
      // passkey login
      final deviceId = SharedPref.getString(deviceIDKey);
      final nonce = await _authRepository.getWalletNonce(deviceId);
      var resultPasskey = await _authPassKeyService.loginWithPasskey(
        email: userName,
        challenge: toBase64Url(nonce.data.message),
      );
      // var resultPasskey = await _authPassKeyService
      //     .loginWithPasskeyConditionalUI(
      //       challenge: toBase64Url(nonce.data.message),
      //     );

      final request = WalletPasskeyRegisterRequestModel(
        userId: userId,
        // address: walletAdress,
        // signature: sign, // cần xem lại  signature ví khi login.
        assertionInfo: WebAuthnResponseModel(
          id: resultPasskey.id,
          rawId: resultPasskey.rawId,
          response: WebAuthnAttestationResponseModel(
            authenticatorData: resultPasskey.authenticatorData,
            clientDataJSON: resultPasskey.clientDataJSON,
            signature: resultPasskey.signature,
            userHandle: resultPasskey.userHandle,
          ),
          type: "public-key",
        ),
      );
      final result = await _authRepository.loginWithPasskey(request);
      SharedPref.setBool(KeyShared.isLogin, true);
      SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
      emit(state.copyWith(status: LoginStatus.success));
    } on DioException catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } on Exception catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } finally {
      EasyLoading.dismiss();
    }
  }

  void getProfileByPassKey() async {
    try {
      EasyLoading.show();
      emit(state.copyWith(status: LoginStatus.loading));
      final deviceId = SharedPref.getString(deviceIDKey);
      final nonce = await _authRepository.getWalletNonce(deviceId);
      var resultPasskey =
          (kIsWeb || Platform.isIOS)
              ? await _authPassKeyService.loginWithPasskey(
                email: "",
                challenge: toBase64Url(nonce.data.message),
              )
              : await _authPassKeyService.loginWithPasskeyConditionalUI(
                challenge: toBase64Url(nonce.data.message),
              );

      final resultProfile = await _authRepository.getProfileByPassKey(
        resultPasskey.id,
      );

      if (resultProfile.isNotEmpty) {
        // for (var element in result) {
        //   KeychainService.instance.setProfile(element);
        // }
        final request = WalletPasskeyRegisterRequestModel(
          userId: resultProfile[0].id,
          // address: walletAdress,
          // signature: sign, // cần xem lại  signature ví khi login.
          assertionInfo: WebAuthnResponseModel(
            id: resultPasskey.id,
            rawId: resultPasskey.rawId,
            response: WebAuthnAttestationResponseModel(
              authenticatorData: resultPasskey.authenticatorData,
              clientDataJSON: resultPasskey.clientDataJSON,
              signature: resultPasskey.signature,
              userHandle: resultPasskey.userHandle,
            ),
            type: "public-key",
          ),
        );
        final result = await _authRepository.loginWithPasskey(request);
        SharedPref.setBool(KeyShared.isLogin, true);
        SharedPref.setString(KeyShared.tokenKey, result.data.accessToken);
        emit(state.copyWith(status: LoginStatus.success));
      } else {
        emit(
          state.copyWith(
            status: LoginStatus.failure,
            message: "No profile found for this passkey",
          ),
        );
      }
    } on DioException catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } on Exception catch (e) {
      emit(state.copyWith(status: LoginStatus.failure, message: e.toString()));
    } finally {
      EasyLoading.dismiss();
    }
  }
}
